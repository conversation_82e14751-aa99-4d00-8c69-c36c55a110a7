package no.ruter.tranop.app.common.dataflow.snowflake

object SnowflakeIngestClientConstants {

    const val COL_REF = "REF"
    const val COL_ASSIGNMENT_REF = "ASSIGNMENT_REF"
    const val COL_OWNER_ID = "OWNER_ID"
    const val COL_CREATED_AT = "CREATED_AT"
    const val COL_AUTHORITY_ID = "AUTHORITY_ID"
    const val COL_OPERATOR_ID = "OPERATOR_ID"
    const val COL_QUAY_REFS = "QUAY_REFS"
    const val COL_DATED_JOURNEY_V2_REF = "DATEDJOURNEYV2_REF"
    const val COL_DATED_JOURNEY_V2_REFS = "DATEDJOURNEYV2_REFS"
    const val COL_LINE_REFS = "LINE_REFS"
    const val COL_JSON_DATA = "JSONDATA"
    const val COL_METADATA = "METADATA"
    const val COL_TYPE = "TYPE"
}
